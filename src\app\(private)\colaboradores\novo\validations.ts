import { z } from 'zod'

import { EPosition } from '@/enum/position'
import { ESeniority } from '@/enum/seniority'
import { globalValidations } from '@/validations/global'

const REQUIRED_FIELD = 'Esse campo é obrigatório'

export const profileSchema = z.object({
  name: globalValidations.text,
  email: globalValidations.email,
  collaboratorType: z
    .string({
      required_error: REQUIRED_FIELD,
    })
    .min(1, {
      message: REQUIRED_FIELD,
    }),
  team: z.string().optional(),
  group: z.string().optional(),
  position: z.string().optional(),
  seniority: z.string().optional(),
  birthdate: globalValidations.date.optional(),
  admitedAt: globalValidations.date.optional(),
})

export type ProfileFormData = z.infer<typeof profileSchema>

export const allPositions: { value: EPosition; label: string }[] = [
  { value: EPosition.APPRENTICE, label: 'Aprendiz' },
  { value: EPosition.INTERN, label: 'Estagiário' },
  { value: EPosition.TRAINEE, label: 'Treinee' },
  { value: EPosition.ASSISTANT, label: 'Assistente' },
  { value: EPosition.AIDE, label: 'Auxiliar' },
  { value: EPosition.ATTENDANT, label: 'Atendente' },
  { value: EPosition.AGENT, label: 'Agente' },
  { value: EPosition.OPERATOR, label: 'Operador' },
  { value: EPosition.MONITOR, label: 'Monitor' },
  { value: EPosition.TECHNICIAN, label: 'Técnico' },
  { value: EPosition.ANALYST, label: 'Analista' },
  { value: EPosition.INSTRUCTOR, label: 'Instrutor' },
  { value: EPosition.SPECIALIST, label: 'Especialista' },
  { value: EPosition.CONSULTANT, label: 'Consultor' },
  { value: EPosition.SUPERVISOR, label: 'Supervisor' },
  { value: EPosition.COORDINATOR, label: 'Coordenador' },
  { value: EPosition.MANAGER, label: 'Gerente' },
  { value: EPosition.DIRECTOR, label: 'Diretor' },
]

export const allSeniorities: { value: ESeniority; label: string }[] = [
  { value: ESeniority.JUNIOR, label: 'Junior' },
  { value: ESeniority.MID_LEVEL, label: 'Pleno' },
  { value: ESeniority.SENIOR, label: 'Sênior' },
]
