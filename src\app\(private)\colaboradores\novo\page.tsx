'use client'

import { But<PERSON>, useAlert } from '@ads/components-react'
import { zodResolver } from '@hookform/resolvers/zod'
import { User } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { Controller, useForm } from 'react-hook-form'
import { useQuery } from 'react-query'

import { PageLayout } from '@/components/layouts/page-layout'
import { DatePicker } from '@/components/ui/date-picker'
import { InputField } from '@/components/ui/input-form'
import { SelectAds } from '@/components/ui/select-ads'
import { Separator } from '@/components/ui/separator'
import { useAuth } from '@/contexts/AuthContext'
import { getGroups } from '@/http/groups'
import { getTeams } from '@/http/teams/get-teams'

import { AvatarSelection } from './components/avatar'
import {
  allPositions,
  allSeniorities,
  ProfileFormData,
  profileSchema,
} from './validations'

export default function Perfil() {
  const router = useRouter()
  const { user } = useAuth()
  const { alert } = useAlert()

  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
  })

  const { data: allTeams, isLoading: isLoadingTeams } = useQuery({
    queryKey: ['getTeams', user],
    queryFn: () =>
      getTeams({
        limit: 999,
        page: 1,
        company_id: user?.metadata?.company_id as number,
      }),
    refetchOnWindowFocus: false,
  })

  const { data: allGroups, isLoading: isLoadingGroups } = useQuery({
    queryKey: ['getGroups', user],
    queryFn: () =>
      getGroups({
        limit: 999,
        page: 1,
        company_id: user?.metadata?.company_id as number,
      }),
    refetchOnWindowFocus: false,
  })

  const onSubmit = async (data: ProfileFormData) => {
    console.log(data)
    try {
      alert({
        alertType: 'success',
        title: 'Perfil salvo',
        description: 'Os dados do colaborador foram salvos com sucesso.',
      })
    } catch (error) {
      console.error('Error saving profile:', error)
      alert({
        alertType: 'danger',
        title: 'Erro ao salvar',
        description: 'Ocorreu um erro ao salvar os dados. Tente novamente.',
      })
    }
  }

  const handleCancel = () => {
    router.push('/colaboradores')
  }

  return (
    <PageLayout
      title="Novo Colaborador"
      description="Gerencie os dados do colaborador desta organização"
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="space-y-8 rounded-md bg-ctx-layout-body px-6 py-10">
          <AvatarSelection />

          <section className="space-y-9">
            <h2 className="flex gap-2 text-ctx-content-title ts-heading-sm">
              <User /> Dados do colaborador
            </h2>

            <div className="grid grid-cols-2 gap-8">
              <InputField
                id="nome"
                label="Nome"
                type="text"
                placeholder="Digite o nome completo"
                errorMessage={errors.name?.message}
                {...register('name')}
              />

              <InputField
                id="email"
                label="Email"
                type="email"
                placeholder="Digite o email"
                errorMessage={errors.email?.message}
                {...register('email')}
              />

              <Controller
                name="team"
                control={control}
                render={({ field }) => (
                  <SelectAds
                    label="Equipe (Opcional)"
                    placeholder="Selecione uma equipe"
                    disabled={
                      isLoadingTeams ||
                      allTeams?.companySquads.data.length === 0
                    }
                    options={
                      allTeams?.companySquads.data.map(({ id, title }) => ({
                        value: String(id),
                        label: title,
                      })) || []
                    }
                    onValueChange={field.onChange}
                    value={field.value}
                  />
                )}
              />

              <Controller
                name="group"
                control={control}
                render={({ field }) => (
                  <SelectAds
                    label="Grupo (Opcional)"
                    placeholder="Selecione um grupo"
                    disabled={
                      isLoadingGroups ||
                      allGroups?.companyGroups.data.length === 0
                    }
                    options={
                      allGroups?.companyGroups.data.map(({ id, name }) => ({
                        value: String(id),
                        label: name,
                      })) || []
                    }
                    onValueChange={field.onChange}
                    value={field.value || ''}
                  />
                )}
              />

              <Controller
                name="collaboratorType"
                control={control}
                render={({ field }) => (
                  <SelectAds
                    label="Tipo de colaborador"
                    placeholder="Selecione o tipo"
                    options={[]}
                    onValueChange={field.onChange}
                    value={field.value}
                    errorMessage={errors.collaboratorType?.message}
                  />
                )}
              />
            </div>

            <Separator />

            <div className="grid grid-cols-2 gap-8">
              <Controller
                name="position"
                control={control}
                render={({ field }) => (
                  <SelectAds
                    label="Cargo (Opcional)"
                    placeholder="Selecione um cargo"
                    options={allPositions}
                    onValueChange={field.onChange}
                    errorMessage={errors.position?.message}
                    value={field.value}
                  />
                )}
              />

              <Controller
                name="seniority"
                control={control}
                render={({ field }) => (
                  <SelectAds
                    label="Nível (Opcional)"
                    placeholder="Selecione um nível"
                    options={allSeniorities}
                    onValueChange={field.onChange}
                    errorMessage={errors.seniority?.message}
                    value={field.value}
                  />
                )}
              />

              <Controller
                name="birthdate"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    label="Data de Nascimento (Opcional)"
                    placeholder="Selecione a data de nascimento"
                    value={field.value as Date}
                    onChange={(date) => field.onChange(date)}
                    errorMessage={errors.birthdate?.message}
                    classNameButton="w-full"
                  />
                )}
              />

              <Controller
                name="admitedAt"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    label="Data de Admissão (Opcional)"
                    placeholder="Selecione a data de admissão"
                    value={field.value as Date}
                    onChange={(date) => field.onChange(date)}
                    errorMessage={errors.admitedAt?.message}
                    classNameButton="w-full"
                  />
                )}
              />
            </div>

            <div className="flex justify-end gap-4">
              <Button
                type="button"
                hierarchy="tertiary"
                onClick={handleCancel}
                disabled={isSubmitting}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                isLoading={isSubmitting}
                disabled={isSubmitting}
              >
                Salvar Alterações
              </Button>
            </div>
          </section>
        </div>
      </form>
    </PageLayout>
  )
}
