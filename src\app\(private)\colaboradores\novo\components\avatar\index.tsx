'use client'

import { <PERSON><PERSON>, But<PERSON>, useAlert } from '@ads/components-react'
import { useState } from 'react'
import { HiOutlinePencil } from 'react-icons/hi2'
import { IoTrashOutline } from 'react-icons/io5'
import { RxAvatar } from 'react-icons/rx'
import { useMutation } from 'react-query'

import { uploadFile } from '@/http/files'

export function AvatarSelection() {
  const { alert } = useAlert()

  const [file, setFile] = useState<File[] | undefined>(undefined)

  const { mutate, isLoading: isUploading } = useMutation({
    mutationFn: () => uploadFile(),
    onSuccess: () => {
      alert({
        title: 'Imagem enviada com sucesso',
        description: 'A imagem foi enviada com sucesso.',
        alertType: 'success',
      })
    },
    onError: () => {
      alert({
        title: 'Erro ao enviar imagem',
        description: 'Ocorreu um erro ao enviar a imagem. Tente novamente.',
        alertType: 'danger',
      })
    },
  })

  const handleUploadImage = () => {
    mutate(file)
    console.log('Upload image')
  }

  return (
    <section className="flex items-center justify-between gap-4 rounded-md bg-ctx-layout-surface p-4">
      <div className="flex items-center justify-center gap-4">
        <div className="flex h-20 w-20 items-center justify-center rounded-full bg-ctx-interactive-secondaryHover">
          {file ? <Avatar src={file} size="xl" /> : <RxAvatar size={36} />}
        </div>
        <div className="flex flex-col gap-1">
          <span className="text-ctx-content-title ts-heading-xxs">
            Foto de perfil
          </span>
          <span className="text-ctx-content-base ts-paragraph-xxxs">
            Tamanho recomendado da foto: 120 x 120 px
          </span>
          <span className="text-ctx-content-base ts-paragraph-xxxs">
            Tamanho do arquivo não superior a 2 MB
          </span>
        </div>
      </div>
      <div className="flex flex-col gap-2">
        <Button
          hierarchy="secondary"
          trailingIcon={HiOutlinePencil}
          onClick={handleUploadImage}
        >
          Alterar Foto
        </Button>
        <Button hierarchy="tertiary" trailingIcon={IoTrashOutline}>
          Excluir Foto
        </Button>
      </div>
    </section>
  )
}
