import { gql } from 'graphql-request'

export const UPDATE_TEAM = gql`
  mutation updateCompanySquad(
    $id: Float!
    $title: String!
    $company_id: Float!
    $user_ids: [Float!]
  ) {
    updateCompanySquad(
      data: {
        id: $id
        title: $title
        company_id: $company_id
        user_ids: $user_ids
      }
    ) {
      id
    }
  }
`

export const DELETE_TEAM = gql`
  mutation deleteCompanySquad($id: Int!) {
    deleteCompanySquad(id: $id)
  }
`
