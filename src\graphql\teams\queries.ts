import { gql } from 'graphql-request'

export const GET_TEAM = gql`
  query getTeamById($id: Int!, $page: Int, $limit: Int) {
    companySquad(id: $id) {
      id
      title
      users(limit: $limit, page: $page) {
        total
        perPage
        data {
          id
          name
          email
          updated_at
        }
      }
    }
  }
`

export const GET_TEAMS = gql`
  query getTeams(
    $limit: Float
    $page: Float
    $company_id: Float
    $title: String
  ) {
    companySquads(
      limit: $limit
      page: $page
      company_id: $company_id
      title: $title
    ) {
      data {
        id
        title
        users_count
        updated_at
      }
      total
      perPage
    }
  }
`

export const GET_B2B_USERS = gql`
  query GetB2BUsers(
    $all: Boolean!
    $page: Float!
    $limit: Float!
    $orderBy: EListOrderBy!
    $order: EListOrder!
    $company_id: Float!
    $enrollment_id: Float!
    $q: String
    $hasSquad: Boolean
  ) {
    users(
      all: $all
      page: $page
      limit: $limit
      orderBy: $orderBy
      order: $order
      company_id: $company_id
      enrollment_id: $enrollment_id
      q: $q
      hasSquad: $hasSquad
    ) {
      total
      perPage
      data {
        id
        name
        email
        last_login
        enrollments_pivot {
          status
        }
        roles {
          id
          name
          slug
        }
        metadata {
          last_activity_completed_at
          activities_completed
          squad_id
          company_squad {
            id
            title
          }
        }
      }
    }
  }
`

export const CREATE_NEW_TEAM = gql`
  mutation CreateCompanySquad(
    $title: String!
    $company_id: Float!
    $user_ids: [Float!]
  ) {
    createCompanySquad(
      data: { title: $title, company_id: $company_id, user_ids: $user_ids }
    ) {
      id
    }
  }
`
