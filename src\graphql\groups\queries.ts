import { gql } from 'graphql-request'

export const GET_GROUPS = gql`
  query getGroups(
    $limit: Float
    $page: Float
    $company_id: Float!
    $name: String
  ) {
    companyGroups(
      limit: $limit
      page: $page
      company_id: $company_id
      name: $name
    ) {
      data {
        id
        name
        company_id
        updated_at
        company_group_users_pivot {
          group_id
          user_id
        }
      }
      total
      perPage
    }
  }
`
