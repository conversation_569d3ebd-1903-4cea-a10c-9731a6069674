'use client'

import { But<PERSON> } from '@ads/components-react'
import { User } from 'lucide-react'

import { PageLayout } from '@/components/layouts/page-layout'
import { InputField } from '@/components/ui/input-form'
import { SelectAds } from '@/components/ui/select-ads'
import { Separator } from '@/components/ui/separator'

export default function Perfil() {
  return (
    <PageLayout
      title="Novo Colaborador"
      description="Gerencie os colaboradores desta organização"
    >
      <div className="space-y-8 rounded-md bg-ctx-layout-body px-6 py-10">
        <section className="h-20 gap-4 rounded-md bg-ctx-layout-surface">
          <h1>Avatar</h1>
        </section>

        <section className="space-y-9">
          <h2 className="flex gap-2 text-ctx-content-title ts-heading-sm">
            <User /> Dados do colaborador
          </h2>

          <div className="grid grid-cols-2 gap-8">
            <InputField label="Nome" />
            <InputField label="Email" />
            <SelectAds label="Equipe (Opcional)" options={[]} />
            <SelectAds label="Grupo (Opcional)" options={[]} />
            <SelectAds label="Tipo de colaborador" options={[]} />
          </div>

          <Separator />

          <div className="grid grid-cols-2 gap-8">
            <SelectAds label="Cargo (Opcional)" options={[]} />
            <SelectAds label="Nivel (Opcional)" options={[]} />
            <InputField label="Data de Nascimento (Opcional)" />
            <InputField label="Data de Admissão (Opcional)" />
          </div>
          <div className="flex justify-end gap-4">
            <Button hierarchy="tertiary">Cancelar</Button>
            <Button>Salvar Alterações</Button>
          </div>
        </section>
      </div>
    </PageLayout>
  )
}
