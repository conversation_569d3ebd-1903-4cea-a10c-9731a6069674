import { GetGroupsQuery } from '@/graphql/generated/graphql'
import { GET_GROUPS } from '@/graphql/groups/queries'
import { clientGraphql } from '@/services/graphql'

type GetGroupsProps = {
  limit?: number
  page?: number
  company_id: number
  name?: string
}

export const getGroups = (data: GetGroupsProps) =>
  clientGraphql.request<GetGroupsQuery>(
    GET_GROUPS,
    {
      ...data,
    },
    {
      'cache-control': 'no-cache',
    }
  )
