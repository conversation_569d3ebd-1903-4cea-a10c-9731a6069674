import { SelectInput, SelectInputItem } from '@ads/components-react'

type SelectAdsProps = {
  options: { value: string; label: string }[]
} & React.ComponentProps<typeof SelectInput>

export function SelectAds({ options, ...props }: SelectAdsProps) {
  return (
    <SelectInput {...props} custom={{ trigger: 'h-10' }}>
      {options.map((option) => (
        <SelectInputItem key={option.value} value={String(option.value)}>
          {option.label}
        </SelectInputItem>
      ))}
    </SelectInput>
  )
}
